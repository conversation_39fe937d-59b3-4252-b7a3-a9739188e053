[0.011s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.098s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.112s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.122s] -- Install configuration: "Release"
[0.123s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch
[0.123s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py
[0.123s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py
[0.124s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch_fixed.py
[0.124s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simple_planner_test.launch.py
[0.124s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config
[0.124s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml
[0.124s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test
[0.125s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv
[0.126s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake
[0.126s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.xml
[0.129s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
