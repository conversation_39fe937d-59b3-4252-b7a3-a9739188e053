[0.066s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'nav2_smac_test', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release']
[0.066s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['nav2_smac_test'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x73814f054c40>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x73814f054880>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x73814f054880>>)
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.162s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.163s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.163s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/nav2'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.173s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.173s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.192s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 469 installed packages in /opt/ros/humble
[0.194s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_cache' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_first' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_force_configure' from command line to 'False'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'ament_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_cmake_args' from command line to 'None'
[0.225s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.225s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_smac_test' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nav2/build/nav2_smac_test', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nav2/install/nav2_smac_test', 'merge_install': False, 'path': '/home/<USER>/nav2/src/nav2_smac_test', 'symlink_install': False, 'test_result_base': None}
[0.225s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.226s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nav2/src/nav2_smac_test' with build type 'ament_cmake'
[0.226s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nav2/src/nav2_smac_test'
[0.227s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.228s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.228s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.234s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.265s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.275s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.285s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.286s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.289s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake module files
[0.289s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake config files
[0.290s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.290s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.291s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.291s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.292s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.293s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.293s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/python3.10/site-packages'
[0.293s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.293s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.294s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.295s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.295s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.296s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.297s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nav2/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.297s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.297s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake module files
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake config files
[0.298s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.298s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.298s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.298s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.299s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.299s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.299s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/python3.10/site-packages'
[0.299s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.300s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.300s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.300s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.300s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.301s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nav2/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.301s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.301s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.304s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.304s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.304s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.315s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.315s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.ps1'
[0.316s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nav2/install/_local_setup_util_ps1.py'
[0.317s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.ps1'
[0.318s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.sh'
[0.318s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nav2/install/_local_setup_util_sh.py'
[0.318s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.sh'
[0.319s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.bash'
[0.319s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.bash'
[0.320s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.zsh'
[0.320s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.zsh'
