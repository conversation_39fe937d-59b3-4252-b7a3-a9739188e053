[0.007s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nav2/src/nav2_smac_test -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nav2/install/nav2_smac_test
[0.068s] -- The C compiler identification is GNU 11.4.0
[0.122s] -- The CXX compiler identification is GNU 11.4.0
[0.131s] -- Detecting C compiler ABI info
[0.207s] -- Detecting C compiler ABI info - done
[0.215s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.215s] -- Detecting C compile features
[0.215s] -- Detecting C compile features - done
[0.217s] -- Detecting CXX compiler ABI info
[0.297s] -- Detecting CXX compiler AB<PERSON> info - done
[0.302s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.303s] -- Detecting CXX compile features
[0.303s] -- Detecting CXX compile features - done
[0.305s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.444s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.522s] -- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)
[0.523s] -- Found nav2_smac_planner: 1.1.18 (/opt/ros/humble/share/nav2_smac_planner/cmake)
[0.554s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.557s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.563s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.571s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.581s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.610s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.612s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.701s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.744s] -- Found FastRTPS: /opt/ros/humble/include  
[0.778s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.784s] -- Looking for pthread.h
[0.858s] -- Looking for pthread.h - found
[0.858s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.943s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.944s] -- Found Threads: TRUE  
[1.141s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.142s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.142s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.252s] -- Found navigation2: 1.1.18 (/opt/ros/humble/share/navigation2/cmake)
[1.253s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.310s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.312s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.313s] -- Added test 'lint_cmake' to check CMake code style
[1.313s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.314s] -- Added test 'xmllint' to check XML markup files
[1.315s] -- Configuring done
[1.318s] -- Generating done
[1.320s] -- Build files have been written to: /home/<USER>/nav2/build/nav2_smac_test
[1.328s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nav2/src/nav2_smac_test -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nav2/install/nav2_smac_test
[1.330s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[1.368s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[1.378s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[1.386s] -- Install configuration: "Release"
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml
[1.386s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv
[1.387s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv
[1.388s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test
[1.388s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake
[1.388s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake
[1.388s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.xml
[1.390s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
