-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)
-- Found nav2_smac_planner: 1.1.18 (/opt/ros/humble/share/nav2_smac_planner/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found navigation2: 1.1.18 (/opt/ros/humble/share/navigation2/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/nav2/build/nav2_smac_test
-- Install configuration: "Release"
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake
-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.xml
