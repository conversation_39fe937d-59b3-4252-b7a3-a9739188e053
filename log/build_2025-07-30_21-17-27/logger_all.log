[0.079s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'nav2_smac_test', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release']
[0.079s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['nav2_smac_test'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7db6ce100ac0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7db6ce100700>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7db6ce100700>>)
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.176s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/nav2'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.184s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ignore', 'ignore_ament_install']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ignore_ament_install'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_pkg']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_pkg'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['colcon_meta']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'colcon_meta'
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extensions ['ros']
[0.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/nav2_smac_test) by extension 'ros'
[0.187s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nav2_smac_test' with type 'ros.ament_cmake' and name 'nav2_smac_test'
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.208s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 435 installed packages in /opt/ros/humble
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_cache' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_clean_first' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'cmake_force_configure' from command line to 'False'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'ament_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_cmake_args' from command line to 'None'
[0.240s] Level 5:colcon.colcon_core.verb:set package 'nav2_smac_test' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.240s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_smac_test' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nav2/build/nav2_smac_test', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nav2/install/nav2_smac_test', 'merge_install': False, 'path': '/home/<USER>/nav2/src/nav2_smac_test', 'symlink_install': False, 'test_result_base': None}
[0.240s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.241s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.241s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nav2/src/nav2_smac_test' with build type 'ament_cmake'
[0.241s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nav2/src/nav2_smac_test'
[0.242s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.242s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.242s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.251s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.305s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.327s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.345s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.345s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake module files
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake config files
[0.348s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.348s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.349s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.349s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.350s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.350s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.350s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/python3.10/site-packages'
[0.350s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.351s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.351s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.352s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.353s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.353s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.354s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nav2/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.354s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_smac_test)
[0.354s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake module files
[0.355s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test' for CMake config files
[0.355s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_smac_test', 'cmake_prefix_path')
[0.355s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.ps1'
[0.356s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.dsv'
[0.356s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/hook/cmake_prefix_path.sh'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/pkgconfig/nav2_smac_test.pc'
[0.357s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/lib/python3.10/site-packages'
[0.357s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nav2/install/nav2_smac_test/bin'
[0.357s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.ps1'
[0.358s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv'
[0.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.sh'
[0.359s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.bash'
[0.359s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.zsh'
[0.359s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nav2/install/nav2_smac_test/share/colcon-core/packages/nav2_smac_test)
[0.360s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.360s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.360s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.360s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.364s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.364s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.364s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.376s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.377s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.ps1'
[0.378s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nav2/install/_local_setup_util_ps1.py'
[0.379s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.ps1'
[0.381s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.sh'
[0.381s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nav2/install/_local_setup_util_sh.py'
[0.382s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.sh'
[0.383s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.bash'
[0.383s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.bash'
[0.384s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nav2/install/local_setup.zsh'
[0.384s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nav2/install/setup.zsh'
