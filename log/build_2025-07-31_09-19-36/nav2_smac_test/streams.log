[0.008s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nav2/src/nav2_smac_test -DCMAKE_INSTALL_PREFIX=/home/<USER>/nav2/install/nav2_smac_test
[0.017s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.108s] -- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)
[0.108s] -- Found nav2_smac_planner: 1.1.18 (/opt/ros/humble/share/nav2_smac_planner/cmake)
[0.131s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.133s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.137s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.144s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.152s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.174s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.174s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.252s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.416s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.417s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.507s] -- Found navigation2: 1.1.18 (/opt/ros/humble/share/navigation2/cmake)
[0.507s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.557s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.558s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.558s] -- Added test 'lint_cmake' to check CMake code style
[0.558s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.559s] -- Added test 'xmllint' to check XML markup files
[0.559s] -- Configuring done
[0.563s] -- Generating done
[0.564s] -- Build files have been written to: /home/<USER>/nav2/build/nav2_smac_test
[0.571s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nav2/src/nav2_smac_test -DCMAKE_INSTALL_PREFIX=/home/<USER>/nav2/install/nav2_smac_test
[0.573s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.610s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nav2/build/nav2_smac_test -- -j16 -l16
[0.625s] Invoking command in '/home/<USER>/nav2/build/nav2_smac_test': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
[0.640s] -- Install configuration: "Release"
[0.640s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch
[0.640s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py
[0.640s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py
[0.640s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch_fixed.py
[0.641s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simple_planner_test.launch.py
[0.641s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config
[0.641s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml
[0.641s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps
[0.641s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh
[0.642s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh
[0.642s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv
[0.642s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh
[0.643s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv
[0.643s] -- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake
[0.643s] -- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.xml
[0.644s] Invoked command in '/home/<USER>/nav2/build/nav2_smac_test' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nav2/build/nav2_smac_test
