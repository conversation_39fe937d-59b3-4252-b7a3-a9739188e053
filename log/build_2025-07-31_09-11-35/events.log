[0.000000] (-) TimerEvent: {}
[0.000217] (nav2_smac_test) JobQueued: {'identifier': 'nav2_smac_test', 'dependencies': OrderedDict()}
[0.000255] (nav2_smac_test) JobStarted: {'identifier': 'nav2_smac_test'}
[0.005226] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'cmake'}
[0.006004] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'build'}
[0.006814] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nav2/build/nav2_smac_test', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nav2/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/nav2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1537'), ('SYSTEMD_EXEC_PID', '1872'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator25ef4b219e3b005583550f2b0f9f990c3'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3661'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:9006'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1676,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1676'), ('INVOCATION_ID', 'a0f787e5f71045c39559099f1766f79e'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.Q4GHA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:4d68fc72-2b4d-481f-90f7-e026b34a03a2'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nav2/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.056738] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.057650] (nav2_smac_test) JobProgress: {'identifier': 'nav2_smac_test', 'progress': 'install'}
[0.068520] (nav2_smac_test) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nav2/build/nav2_smac_test'], 'cwd': '/home/<USER>/nav2/build/nav2_smac_test', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/nav2'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('MANAGERPID', '1537'), ('SYSTEMD_EXEC_PID', '1872'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('TERMINATOR_DBUS_NAME', 'net.tenshu.Terminator25ef4b219e3b005583550f2b0f9f990c3'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '3661'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('JOURNAL_STREAM', '8:9006'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1676,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1676'), ('INVOCATION_ID', 'a0f787e5f71045c39559099f1766f79e'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('TERMINATOR_DBUS_PATH', '/net/tenshu/Terminator2'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.Q4GHA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('TERMINATOR_UUID', 'urn:uuid:4d68fc72-2b4d-481f-90f7-e026b34a03a2'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nav2/build/nav2_smac_test'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.078408] (nav2_smac_test) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[0.078619] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch\n'}
[0.078707] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch.py\n'}
[0.078811] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/smac_planner_test.launch.py\n'}
[0.078952] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simulation_launch_fixed.py\n'}
[0.079033] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//launch/simple_planner_test.launch.py\n'}
[0.079100] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config\n'}
[0.079167] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//config/nav2_params.yaml\n'}
[0.079232] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps\n'}
[0.079301] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.pgm\n'}
[0.079368] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//maps/test_map.yaml\n'}
[0.079431] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds\n'}
[0.079494] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//worlds/test_world.world\n'}
[0.079555] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf\n'}
[0.079616] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test//urdf/robot.urdf.xacro\n'}
[0.079706] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/package_run_dependencies/nav2_smac_test\n'}
[0.079833] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/parent_prefix_path/nav2_smac_test\n'}
[0.079906] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.sh\n'}
[0.079973] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/ament_prefix_path.dsv\n'}
[0.080036] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.sh\n'}
[0.080098] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/environment/path.dsv\n'}
[0.080160] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.bash\n'}
[0.080220] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.sh\n'}
[0.080283] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.zsh\n'}
[0.080358] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/local_setup.dsv\n'}
[0.080419] (nav2_smac_test) StdoutLine: {'line': b'-- Installing: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.dsv\n'}
[0.080524] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/ament_index/resource_index/packages/nav2_smac_test\n'}
[0.080588] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig.cmake\n'}
[0.080649] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/cmake/nav2_smac_testConfig-version.cmake\n'}
[0.080705] (nav2_smac_test) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nav2/install/nav2_smac_test/share/nav2_smac_test/package.xml\n'}
[0.082492] (nav2_smac_test) CommandEnded: {'returncode': 0}
[0.094262] (nav2_smac_test) JobEnded: {'identifier': 'nav2_smac_test', 'rc': 0}
[0.094752] (-) EventReactorShutdown: {}
