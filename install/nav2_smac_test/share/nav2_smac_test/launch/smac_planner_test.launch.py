import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Get the package directory
    pkg_share = FindPackageShare(package='nav2_smac_test').find('nav2_smac_test')
    
    # Path to navigation parameters
    nav2_params_path = PathJoinSubstitution([pkg_share, 'config', 'nav2_params.yaml'])
    map_yaml_path = PathJoinSubstitution([pkg_share, 'maps', 'test_map.yaml'])
    
    # Launch configuration variables
    use_sim_time = LaunchConfiguration('use_sim_time')

    # Declare launch arguments
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true')

    # Map server
    map_server_cmd = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[{'use_sim_time': use_sim_time,
                    'yaml_filename': map_yaml_path}])

    # AMCL for localization
    amcl_cmd = Node(
        package='nav2_amcl',
        executable='amcl',
        name='amcl',
        output='screen',
        parameters=[nav2_params_path])

    # Static transform publisher for odom->base_link (for demo purposes)
    static_transform_cmd = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'odom', 'base_link'])

    # Initial pose publisher (sets robot position in map)
    initial_pose_cmd = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom'])

    # Planner server (Smac Planner)
    planner_server_cmd = Node(
        package='nav2_planner',
        executable='planner_server',
        name='planner_server',
        output='screen',
        parameters=[nav2_params_path])

    # Costmap 2D (for global costmap)
    global_costmap_cmd = Node(
        package='nav2_costmap_2d',
        executable='nav2_costmap_2d',
        name='global_costmap',
        output='screen',
        parameters=[nav2_params_path],
        remappings=[('~/costmap_raw', '/global_costmap/costmap_raw')])

    # Lifecycle manager for the navigation nodes
    lifecycle_manager_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_navigation',
        output='screen',
        parameters=[{'use_sim_time': use_sim_time,
                    'autostart': True,
                    'node_names': ['map_server',
                                  'amcl',
                                  'planner_server',
                                  'global_costmap']}])

    # Create the launch description and populate
    ld = LaunchDescription()

    # Add the commands to the launch description
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(map_server_cmd)
    ld.add_action(amcl_cmd)
    ld.add_action(static_transform_cmd)
    ld.add_action(initial_pose_cmd)
    ld.add_action(planner_server_cmd)
    ld.add_action(global_costmap_cmd)
    ld.add_action(lifecycle_manager_cmd)

    return ld