set(_AMENT_PACKAGE_NAME "nav2_smac_test")
set(nav2_smac_test_VERSION "1.0.0")
set(nav2_smac_test_MAINTAINER "User <<EMAIL>>")
set(nav2_smac_test_BUILD_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "geometry_msgs" "sensor_msgs" "std_msgs" "rclcpp" "rclpy")
set(nav2_smac_test_BUILDTOOL_DEPENDS "ament_cmake")
set(nav2_smac_test_BUILD_EXPORT_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "geometry_msgs" "sensor_msgs" "std_msgs" "rclcpp" "rclpy")
set(nav2_smac_test_BUILDTOOL_EXPORT_DEPENDS )
set(nav2_smac_test_EXEC_DEPENDS "nav2_bringup" "nav2_common" "nav2_smac_planner" "navigation2" "gazebo_ros_pkgs" "robot_state_publisher" "xacro" "tf2" "tf2_ros" "geometry_msgs" "sensor_msgs" "std_msgs" "rclcpp" "rclpy")
set(nav2_smac_test_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(nav2_smac_test_GROUP_DEPENDS )
set(nav2_smac_test_MEMBER_OF_GROUPS )
set(nav2_smac_test_DEPRECATED "")
set(nav2_smac_test_EXPORT_TAGS)
list(APPEND nav2_smac_test_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
