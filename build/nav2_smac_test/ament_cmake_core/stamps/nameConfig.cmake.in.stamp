# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_@PROJECT_NAME@_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED @PROJECT_NAME@_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(@PROJECT_NAME@_FOUND FALSE)
  elseif(NOT @PROJECT_NAME@_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(@PROJECT_NAME@_FOUND FALSE)
  endif()
  return()
endif()
set(_@PROJECT_NAME@_CONFIG_INCLUDED TRUE)

# output package information
if(NOT @PROJECT_NAME@_FIND_QUIETLY)
  message(STATUS "Found @PROJECT_NAME@: @PACKAGE_VERSION@ (${@PROJECT_NAME@_DIR})")
endif()

# warn when using a deprecated package
if(NOT "@PACKAGE_DEPRECATED@" STREQUAL "")
  set(_msg "Package '@PROJECT_NAME@' is deprecated")
  # append custom deprecation text if available
  if(NOT "@PACKAGE_DEPRECATED@" STREQUAL "TRUE")
    set(_msg "${_msg} (@PACKAGE_DEPRECATED@)")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${@PROJECT_NAME@_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(@PROJECT_NAME@_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "@PACKAGE_CONFIG_EXTRA_FILES@")
foreach(_extra ${_extras})
  include("${@PROJECT_NAME@_DIR}/${_extra}")
endforeach()
