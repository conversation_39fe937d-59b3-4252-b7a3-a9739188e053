#!/bin/bash

# Nav2 Smac Planner Test Project Build Script
# Usage: ./build.sh [clean|test|run]

set -e

PROJECT_ROOT="/home/<USER>/nav2"
cd "$PROJECT_ROOT"

echo "=== Nav2 Smac Planner Test Project Build Script ==="

case "${1:-build}" in
    "clean")
        echo "Cleaning build artifacts..."
        rm -rf build/ install/ log/
        echo "Clean complete."
        ;;
    
    "build")
        echo "Building nav2_smac_test package..."
        colcon build --packages-select nav2_smac_test --cmake-args -DCMAKE_BUILD_TYPE=Release
        echo "Build complete."
        echo "Source the workspace: source install/setup.bash"
        ;;
    
    "test")
        echo "Building and running automated tests..."
        colcon build --packages-select nav2_smac_test --cmake-args -DCMAKE_BUILD_TYPE=Release
        source install/setup.bash
        
        echo "Starting simulation in background..."
        ros2 launch nav2_smac_test simulation_launch.py use_rviz:=false use_gazebo_gui:=false &
        LAUNCH_PID=$!
        
        # Wait for simulation to start
        echo "Waiting for simulation to initialize..."
        sleep 15
        
        echo "Running test script..."
        python3 src/nav2_smac_test/scripts/test_smac_planner.py
        
        # Kill the launch process
        echo "Stopping simulation..."
        kill $LAUNCH_PID
        wait $LAUNCH_PID 2>/dev/null || true
        ;;
    
    "run")
        echo "Building and launching simulation..."
        colcon build --packages-select nav2_smac_test --cmake-args -DCMAKE_BUILD_TYPE=Release
        source install/setup.bash
        ros2 launch nav2_smac_test simulation_launch_fixed.py
        ;;
    
    "run-original")
        echo "Building and launching original simulation..."
        colcon build --packages-select nav2_smac_test --cmake-args -DCMAKE_BUILD_TYPE=Release
        source install/setup.bash
        ros2 launch nav2_smac_test simulation_launch.py
        ;;
    
    "run-simple")
        echo "Building and launching simple planner test..."
        colcon build --packages-select nav2_smac_test --cmake-args -DCMAKE_BUILD_TYPE=Release
        source install/setup.bash
        ros2 launch nav2_smac_test simple_planner_test.launch.py
        ;;
    
    *)
        echo "Usage: $0 [clean|build|test|run|run-original|run-simple]"
        echo ""
        echo "Commands:"
        echo "  clean        - Clean build artifacts"
        echo "  build        - Build the project (default)"
        echo "  test         - Build and run automated tests"
        echo "  run          - Build and launch fixed simulation (recommended)"
        echo "  run-original - Build and launch original simulation"
        echo "  run-simple   - Build and launch simple planner test"
        exit 1
        ;;
esac

echo "=== Script complete ==="