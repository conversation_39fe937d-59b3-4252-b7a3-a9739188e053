# Nav2 Smac Planner Test Project

这个ROS2项目用于测试和评估Navigation2框架中的Smac Planner全局路径规划算法。项目包含完整的仿真环境、配置文件和测试脚本。

## 项目概述

**Smac Planner**是Navigation2中的一个高级路径规划器，支持多种搜索算法：
- **Smac Planner 2D**: 基于A*的2D路径规划
- **Smac Planner Hybrid**: 混合A*算法，考虑车辆运动约束
- **Smac Planner Lattice**: 基于状态网格的路径规划

本项目默认配置为**SmacPlannerHybrid**，适合类车机器人的路径规划。

## 项目结构

```
nav2_smac_test/
├── CMakeLists.txt
├── package.xml
├── config/
│   └── nav2_params.yaml          # Navigation2参数配置
├── launch/
│   ├── simulation_launch.py      # 完整仿真启动文件
│   └── smac_planner_test.launch.py  # 单独测试Smac Planner
├── maps/
│   ├── test_map.pgm             # 测试地图图像
│   └── test_map.yaml            # 地图元数据
├── scripts/
│   └── test_smac_planner.py     # 自动化测试脚本
├── urdf/
│   └── robot.urdf.xacro         # 机器人模型描述
└── worlds/
    └── test_world.world         # Gazebo仿真世界
```

## 安装依赖

确保已安装以下ROS2包：

```bash
sudo apt update
sudo apt install ros-$ROS_DISTRO-navigation2 \
                 ros-$ROS_DISTRO-nav2-bringup \
                 ros-$ROS_DISTRO-nav2-smac-planner \
                 ros-$ROS_DISTRO-gazebo-ros-pkgs \
                 ros-$ROS_DISTRO-robot-state-publisher \
                 ros-$ROS_DISTRO-xacro
```

## 编译项目

```bash
cd /home/<USER>/nav2
colcon build --packages-select nav2_smac_test
source install/setup.bash
```

## 运行方式

### 1. 完整仿真测试

启动Gazebo仿真环境和完整的Navigation2堆栈：

```bash
ros2 launch nav2_smac_test simulation_launch.py
```

启动参数选项：
- `use_rviz:=false` - 禁用RViz可视化
- `use_gazebo_gui:=false` - 禁用Gazebo GUI
- `x_pose:=1.0 y_pose:=1.0` - 设置机器人初始位置

### 2. 单独测试Smac Planner

只启动路径规划相关组件：

```bash
ros2 launch nav2_smac_test smac_planner_test.launch.py
```

### 3. 自动化测试

运行自动化测试脚本：

```bash
# 首先启动仿真环境
ros2 launch nav2_smac_test simulation_launch.py

# 在新终端运行测试脚本
python3 /home/<USER>/nav2/src/nav2_smac_test/scripts/test_smac_planner.py
```

## Smac Planner配置说明

### 核心参数配置

项目中的`config/nav2_params.yaml`包含了详细的Smac Planner配置：

#### 算法选择
```yaml
plugin: "nav2_smac_planner/SmacPlannerHybrid"  # 可选: SmacPlanner2D, SmacPlannerLattice
```

#### 性能参数
- `max_planning_time: 5.0` - 最大规划时间（秒）
- `max_iterations: 1000000` - 最大搜索迭代次数
- `tolerance: 0.25` - 目标容忍度（米）

#### 运动模型
- `motion_model_for_search: "DUBIN"` - 搜索运动模型
  - MOORE: 8连接网格
  - VON_NEUMANN: 4连接网格
  - DUBIN: Dubin路径模型
  - REEDS_SHEPP: Reeds-Shepp路径模型

#### 车辆约束
- `minimum_turning_radius: 0.40` - 最小转弯半径（米）
- `angle_quantization_bins: 72` - 角度量化级数

#### 代价函数
- `reverse_penalty: 2.0` - 倒车惩罚
- `change_penalty: 0.0` - 方向改变惩罚
- `non_straight_penalty: 1.2` - 非直线运动惩罚

## 测试场景

自动化测试脚本包含5个测试用例：

1. **前进右转** (3.0, 3.0, 0°)
2. **前进左转** (-3.0, 3.0, 90°)
3. **后退左转** (-3.0, -3.0, 180°)
4. **后退右转** (3.0, -3.0, -90°)
5. **返回原点** (0.0, 0.0, 0°)

测试评估指标：
- 成功率
- 平均规划时间
- 路径质量

## 调试和优化

### 可视化调试

在RViz中可以查看：
- 全局路径（绿色线条）
- 局部路径（红色线条）
- 代价地图
- 激光雷达数据

### 性能优化建议

1. **大地图优化**：
   ```yaml
   downsample_costmap: true
   downsampling_factor: 2
   ```

2. **实时性优化**：
   ```yaml
   max_planning_time: 2.0
   max_iterations: 500000
   ```

3. **路径质量优化**：
   ```yaml
   smooth_path: true
   analytic_expansion_ratio: 5.0
   ```

## 算法对比测试

可以通过修改配置文件测试不同的Smac Planner变体：

### Smac Planner 2D
```yaml
plugin: "nav2_smac_planner/SmacPlanner2D"
motion_model_for_search: "MOORE"  # 或 "VON_NEUMANN"
```

### Smac Planner Lattice
```yaml
plugin: "nav2_smac_planner/SmacPlannerLattice"
# 需要额外的lattice配置文件
```

## 常见问题

### 1. 规划失败
- 检查地图是否正确加载
- 确认起点和终点在可行区域内
- 调整`tolerance`参数

### 2. 规划时间过长
- 减少`max_iterations`
- 启用`downsample_costmap`
- 调整`max_planning_time`

### 3. 路径质量不佳
- 启用`smooth_path`
- 调整代价函数参数
- 增加`analytic_expansion_ratio`

## 扩展功能

### 添加自定义地图
1. 将新地图文件放入`maps/`目录
2. 修改launch文件中的地图路径
3. 重新编译和启动

### 修改机器人模型
1. 编辑`urdf/robot.urdf.xacro`
2. 调整`nav2_params.yaml`中的机器人参数
3. 重新编译

### 添加新的测试场景
1. 修改`scripts/test_smac_planner.py`
2. 在`test_goals`列表中添加新目标
3. 运行测试脚本

## 参考资料

- [Navigation2 官方文档](https://navigation.ros.org/)
- [Smac Planner 详细说明](https://navigation.ros.org/configuration/packages/smac/configuring-smac-planner.html)
- [ROS2 官方教程](https://docs.ros.org/en/humble/Tutorials.html)

## 贡献和反馈

如需改进或发现问题，请提交Issue或Pull Request。

---

**作者**: ultrathink  
**日期**: 2024  
**ROS版本**: ROS2 Humble/Iron/Jazzy