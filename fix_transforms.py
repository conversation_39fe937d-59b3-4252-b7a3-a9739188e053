#!/usr/bin/env python3
"""
Quick fix script to publish missing transforms for nav2 demo
"""
import rclpy
from rclpy.node import Node
from tf2_ros.static_transform_broadcaster import StaticTransformBroadcaster
from geometry_msgs.msg import TransformStamped
import tf_transformations

class TransformPublisher(Node):
    def __init__(self):
        super().__init__('fix_transforms')
        self.tf_broadcaster = StaticTransformBroadcaster(self)
        
        # Publish map -> odom transform
        map_to_odom = TransformStamped()
        map_to_odom.header.frame_id = 'map'
        map_to_odom.child_frame_id = 'odom'
        map_to_odom.transform.translation.x = 0.0
        map_to_odom.transform.translation.y = 0.0
        map_to_odom.transform.translation.z = 0.0
        map_to_odom.transform.rotation.x = 0.0
        map_to_odom.transform.rotation.y = 0.0
        map_to_odom.transform.rotation.z = 0.0
        map_to_odom.transform.rotation.w = 1.0
        
        # Publish odom -> base_link transform
        odom_to_base = TransformStamped()
        odom_to_base.header.frame_id = 'odom'
        odom_to_base.child_frame_id = 'base_link'
        odom_to_base.transform.translation.x = 0.0
        odom_to_base.transform.translation.y = 0.0
        odom_to_base.transform.translation.z = 0.0
        odom_to_base.transform.rotation.x = 0.0
        odom_to_base.transform.rotation.y = 0.0
        odom_to_base.transform.rotation.z = 0.0
        odom_to_base.transform.rotation.w = 1.0
        
        self.tf_broadcaster.sendTransform([map_to_odom, odom_to_base])
        self.get_logger().info('Publishing map->odom->base_link transforms')

def main():
    rclpy.init()
    node = TransformPublisher()
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()