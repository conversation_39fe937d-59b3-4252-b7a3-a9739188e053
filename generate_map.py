#!/usr/bin/env python3
"""
Simple map generator for Smac Planner testing
Creates a PGM map file with obstacles for path planning tests
"""

import numpy as np
from PIL import Image
import os

def create_test_map():
    """Create a test map with obstacles for Smac Planner testing."""
    
    # Map dimensions (pixels)
    width = 400
    height = 400
    
    # Create empty map (255 = free space, 0 = obstacle, 128 = unknown)
    map_data = np.full((height, width), 255, dtype=np.uint8)
    
    # Add border walls (thickness of 5 pixels)
    wall_thickness = 5
    map_data[:wall_thickness, :] = 0  # Top wall
    map_data[-wall_thickness:, :] = 0  # Bottom wall
    map_data[:, :wall_thickness] = 0  # Left wall
    map_data[:, -wall_thickness:] = 0  # Right wall
    
    # Add some obstacles for testing
    # Rectangle obstacle
    map_data[280:320, 280:320] = 0
    
    # Circular obstacle
    center_x, center_y = 120, 120
    radius = 25
    for y in range(height):
        for x in range(width):
            if (x - center_x)**2 + (y - center_y)**2 <= radius**2:
                map_data[y, x] = 0
    
    # L-shaped obstacle
    map_data[250:300, 120:140] = 0  # Vertical part
    map_data[280:300, 140:200] = 0  # Horizontal part
    
    # Narrow passage
    map_data[190:210, 200:350] = 0  # Top part of passage
    map_data[190:210, 200:220] = 255  # Opening in passage
    
    return map_data

def save_map(map_data, map_dir):
    """Save the map as PGM and YAML files."""
    
    # Ensure directory exists
    os.makedirs(map_dir, exist_ok=True)
    
    # Save PGM file
    pgm_path = os.path.join(map_dir, 'test_map.pgm')
    image = Image.fromarray(map_data, mode='L')
    image.save(pgm_path)
    
    # Create YAML file content
    yaml_content = f"""image: test_map.pgm
resolution: 0.05
origin: [-10.0, -10.0, 0.0]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196
"""
    
    # Save YAML file
    yaml_path = os.path.join(map_dir, 'test_map.yaml')
    with open(yaml_path, 'w') as f:
        f.write(yaml_content)
    
    print(f"Map saved to {map_dir}")
    print(f"Map size: {map_data.shape[1]}x{map_data.shape[0]} pixels")
    print(f"Resolution: 0.05 m/pixel")
    print(f"Real world size: {map_data.shape[1]*0.05:.1f}x{map_data.shape[0]*0.05:.1f} meters")

def main():
    """Main function to generate and save the test map."""
    # Get the maps directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    maps_dir = os.path.join(project_root, 'src', 'nav2_smac_test', 'maps')
    
    print("Generating test map for Smac Planner...")
    
    # Create the map
    map_data = create_test_map()
    
    # Save the map
    save_map(map_data, maps_dir)
    
    print("Map generation complete!")

if __name__ == '__main__':
    main()