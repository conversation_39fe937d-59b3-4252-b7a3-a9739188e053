#!/usr/bin/env python3
"""
Smac Planner Test Script
This script tests the Smac Planner by sending navigation goals and measuring performance.
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile
from geometry_msgs.msg import PoseStamped, PoseWithCovarianceStamped
from nav2_msgs.action import NavigateToPose
from rclpy.action import ActionClient
from rclpy.executors import MultiThreadedExecutor
import time
import math


class SmacPlannerTester(Node):
    def __init__(self):
        super().__init__('smac_planner_tester')
        
        # Action client for navigation
        self.nav_to_pose_client = ActionClient(self, NavigateToPose, 'navigate_to_pose')
        
        # Publisher for initial pose
        self.initial_pose_pub = self.create_publisher(
            PoseWithCovarianceStamped, 
            'initialpose', 
            QoSProfile(depth=1)
        )
        
        # Test goals (x, y, yaw)
        self.test_goals = [
            (3.0, 3.0, 0.0),    # Goal 1: Forward right
            (-3.0, 3.0, 1.57),  # Goal 2: Forward left, face up
            (-3.0, -3.0, 3.14), # Goal 3: Back left, face back
            (3.0, -3.0, -1.57), # Goal 4: Back right, face down
            (0.0, 0.0, 0.0),    # Goal 5: Return to origin
        ]
        
        self.current_goal_index = 0
        self.test_results = []
        
        self.get_logger().info("Smac Planner Tester initialized")
        
    def set_initial_pose(self):
        """Set the initial pose of the robot."""
        initial_pose = PoseWithCovarianceStamped()
        initial_pose.header.frame_id = 'map'
        initial_pose.header.stamp = self.get_clock().now().to_msg()
        
        # Set pose at origin
        initial_pose.pose.pose.position.x = 0.0
        initial_pose.pose.pose.position.y = 0.0
        initial_pose.pose.pose.position.z = 0.0
        initial_pose.pose.pose.orientation.w = 1.0
        
        # Set covariance (identity matrix scaled by 0.25)
        initial_pose.pose.covariance = [0.25] * 36
        
        self.initial_pose_pub.publish(initial_pose)
        self.get_logger().info("Initial pose set")
        
    def create_goal_pose(self, x, y, yaw):
        """Create a PoseStamped goal."""
        goal_pose = PoseStamped()
        goal_pose.header.frame_id = 'map'
        goal_pose.header.stamp = self.get_clock().now().to_msg()
        
        goal_pose.pose.position.x = x
        goal_pose.pose.position.y = y
        goal_pose.pose.position.z = 0.0
        
        # Convert yaw to quaternion
        goal_pose.pose.orientation.z = math.sin(yaw / 2.0)
        goal_pose.pose.orientation.w = math.cos(yaw / 2.0)
        
        return goal_pose
        
    def send_goal(self, x, y, yaw):
        """Send a navigation goal."""
        self.get_logger().info(f"Sending goal: x={x}, y={y}, yaw={yaw}")
        
        # Wait for action server
        if not self.nav_to_pose_client.wait_for_server(timeout_sec=10.0):
            self.get_logger().error("Navigate to pose action server not available!")
            return False
            
        # Create goal
        goal_msg = NavigateToPose.Goal()
        goal_msg.pose = self.create_goal_pose(x, y, yaw)
        
        # Send goal and measure time
        start_time = time.time()
        future = self.nav_to_pose_client.send_goal_async(goal_msg)
        
        rclpy.spin_until_future_complete(self, future)
        goal_handle = future.result()
        
        if not goal_handle.accepted:
            self.get_logger().error("Goal rejected!")
            return False
            
        self.get_logger().info("Goal accepted, waiting for result...")
        
        # Wait for result
        result_future = goal_handle.get_result_async()
        rclpy.spin_until_future_complete(self, result_future)
        
        result = result_future.result()
        end_time = time.time()
        planning_time = end_time - start_time
        
        # Log results
        success = result.status == 4  # SUCCEEDED
        self.test_results.append({
            'goal': (x, y, yaw),
            'success': success,
            'planning_time': planning_time
        })
        
        if success:
            self.get_logger().info(f"Goal reached! Planning time: {planning_time:.2f}s")
        else:
            self.get_logger().error(f"Goal failed! Status: {result.status}")
            
        return success
        
    def run_test_sequence(self):
        """Run the complete test sequence."""
        self.get_logger().info("Starting Smac Planner test sequence...")
        
        # Set initial pose
        time.sleep(1.0)  # Wait for publisher to be ready
        self.set_initial_pose()
        time.sleep(2.0)  # Wait for localization
        
        # Test each goal
        for i, (x, y, yaw) in enumerate(self.test_goals):
            self.get_logger().info(f"Test {i+1}/{len(self.test_goals)}")
            success = self.send_goal(x, y, yaw)
            
            if not success:
                self.get_logger().warning(f"Test {i+1} failed, continuing...")
            
            time.sleep(2.0)  # Wait between goals
            
        # Print summary
        self.print_test_summary()
        
    def print_test_summary(self):
        """Print test results summary."""
        self.get_logger().info("=== SMAC PLANNER TEST RESULTS ===")
        
        successful_tests = 0
        total_planning_time = 0.0
        
        for i, result in enumerate(self.test_results):
            goal = result['goal']
            success = result['success']
            planning_time = result['planning_time']
            
            status = "SUCCESS" if success else "FAILED"
            self.get_logger().info(
                f"Test {i+1}: Goal({goal[0]}, {goal[1]}, {goal[2]:.2f}) - "
                f"{status} - {planning_time:.2f}s"
            )
            
            if success:
                successful_tests += 1
                total_planning_time += planning_time
                
        # Calculate statistics
        success_rate = (successful_tests / len(self.test_results)) * 100
        avg_planning_time = total_planning_time / successful_tests if successful_tests > 0 else 0
        
        self.get_logger().info(f"Success Rate: {success_rate:.1f}% ({successful_tests}/{len(self.test_results)})")
        self.get_logger().info(f"Average Planning Time: {avg_planning_time:.2f}s")
        self.get_logger().info("=== TEST COMPLETE ===")


def main(args=None):
    rclpy.init(args=args)
    
    tester = SmacPlannerTester()
    executor = MultiThreadedExecutor()
    
    try:
        # Wait a bit for all nodes to be ready
        time.sleep(5.0)
        
        # Run the test sequence
        tester.run_test_sequence()
        
    except KeyboardInterrupt:
        tester.get_logger().info("Test interrupted by user")
    finally:
        tester.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()