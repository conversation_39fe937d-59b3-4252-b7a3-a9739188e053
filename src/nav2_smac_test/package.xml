<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>nav2_smac_test</name>
  <version>1.0.0</version>
  <description>ROS2 package for testing Navigation2 Smac Planner algorithm</description>
  <maintainer email="<EMAIL>">User</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <!-- Navigation2 dependencies -->
  <depend>nav2_bringup</depend>
  <depend>nav2_common</depend>
  <depend>nav2_smac_planner</depend>
  <depend>navigation2</depend>
  
  <!-- Simulation dependencies -->
  <depend>gazebo_ros_pkgs</depend>
  <depend>robot_state_publisher</depend>
  <depend>xacro</depend>
  
  <!-- Other dependencies -->
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>geometry_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>rclcpp</depend>
  <depend>rclpy</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>